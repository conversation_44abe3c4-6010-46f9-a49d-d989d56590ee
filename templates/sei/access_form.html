<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acesso Seguro ao Protocolo - Sistema SEI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#FF161F',
                        'primary-light': '#ffeeee',
                        'secondary': '#034EA2',
                        'accent': '#0B9247',
                        'accent-dark': '#047857',
                        'highlight': '#FBB900',
                        'gray-darkest': '#000000',
                        'gray-darker': '#333333',
                        'gray-dark': '#4b5563',
                        'gray-medium': '#6b7280',
                        'gray-light': '#9ca3af',
                        'gray-lighter': '#d1d5db',
                        'gray-lightest': '#f3f4f6'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-gray-darkest shadow-lg border-b border-gray-dark">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-white">ARTESP - Sistema GERPRO</h1>
                    <span class="ml-4 text-gray-light">Acesso Seguro ao Protocolo</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{% url 'sei:sei_form' %}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Novo Protocolo
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Instructions -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-blue-800">Como Acessar Seu Protocolo</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Use o e-mail que você forneceu ao criar o protocolo</li>
                            <li>Digite a senha de acesso que foi enviada por e-mail</li>
                            <li>Você poderá editar apenas o campo "Número SEI" do protocolo</li>
                            <li>A senha é válida por 7 dias a partir da criação do protocolo</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Access Form -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <span class="bg-secondary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">🔐</span>
                Acesso Seguro ao Protocolo
            </h2>

            <!-- Display Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="mb-4 p-4 rounded-md {% if message.tags == 'error' %}bg-red-50 border border-red-200 text-red-700{% elif message.tags == 'success' %}bg-green-50 border border-green-200 text-green-700{% else %}bg-blue-50 border border-blue-200 text-blue-700{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Email Field -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        E-mail do Requisitante
                    </label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-secondary focus:border-secondary"
                           placeholder="Digite seu e-mail"
                           value="{{ request.POST.email|default:'' }}">
                    <p class="mt-1 text-sm text-gray-500">
                        Use o mesmo e-mail fornecido ao criar o protocolo
                    </p>
                </div>

                <!-- Password Field -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        Senha de Acesso
                    </label>
                    <input type="text" 
                           id="password" 
                           name="password" 
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-secondary focus:border-secondary font-mono"
                           placeholder="Digite a senha enviada por e-mail"
                           maxlength="12">
                    <p class="mt-1 text-sm text-gray-500">
                        Senha de 10 caracteres enviada por e-mail após a criação do protocolo
                    </p>
                </div>

                <!-- Security Notice -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Aviso de Segurança</h3>
                            <div class="mt-1 text-sm text-yellow-700">
                                <p>Por motivos de segurança, você tem no máximo 5 tentativas de acesso. Após isso, será necessário aguardar alguns minutos antes de tentar novamente.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-center">
                    <button type="submit"
                            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-secondary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-colors">
                            Acessar Protocolo
                            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="transform: scaleX(-1);">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                            </svg>
                    </button>
                </div>
            </form>
        </div>

        <!-- Help Section -->
        <div class="mt-8 bg-gray-50 rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Precisa de Ajuda?</h3>
            <div class="text-sm text-gray-600 space-y-2">
                <p><strong>Não recebeu o e-mail com a senha?</strong> Verifique sua caixa de spam ou lixo eletrônico.</p>
                <p><strong>Senha expirada?</strong> As senhas são válidas por 7 dias. Após esse período, será necessário criar um novo protocolo.</p>
                <p><strong>Esqueceu o e-mail usado?</strong> Use o mesmo e-mail fornecido ao criar o protocolo original.</p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-darkest text-white mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="text-center">
                <p class="text-sm text-gray-light">
                    © 2025 ARTESP - Sistema GERPRO - Todos os direitos reservados
                </p>
            </div>
        </div>
    </footer>
</body>
</html>
